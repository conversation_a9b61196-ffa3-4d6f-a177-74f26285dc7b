# -*- coding: utf-8 -*-
"""
Created on Wed Aug  6 21:14:19 2025

@author: licha
"""

import cadquery as cq
from OCP.BRepGProp import BRepGProp  # 从 OpenCASCADE 的 GProp 模块中导入 BRepGProp 类，用于计算几何体的体积属性
from OCP.GProp import GProp_GProps   # 从 OpenCASCADE 的 GProp 模块中导入 GProp_GProps 类 ，它是用于接受质量属性计算结果的容器
import pyvista as pv
import tempfile
import os

class MassPropertiesOcpC:
    """
    基于OCP的质量属性计算器类
    用于计算几何体的质量、质心、惯性张量等物理属性
    """
    
    def __init__(self, densityMaterial=2350.0):
        """
        初始化质量属性计算器
        
        Args:
            densityMaterial (float): 材料密度，单位 kg/m³，默认为2350
        """
        self.densityMaterial = densityMaterial
    
    def calculateMassPropertiesF(self, cadqueryObject):
        """
        计算CadQuery对象的质量属性
        
        Args:
            cadqueryObject: CadQuery工作平面对象或实体对象
            
        Returns:
            dict: 包含质量属性的字典
        """
        try:
            # 获取实体对象
            if hasattr(cadqueryObject, 'val'):
                solidObject = cadqueryObject.val()
            else:
                solidObject = cadqueryObject
            
            # 如果是Compound对象，尝试获取第一个Solid
            if hasattr(solidObject, 'ShapeType') and solidObject.ShapeType() == 0:  # Compound
                # 尝试从CadQuery对象获取solids
                if hasattr(cadqueryObject, 'solids'):
                    solids = cadqueryObject.solids()
                    if solids.size() > 0:
                        solidObject = solids.val()
            
            # 如果是Compound对象，尝试获取第一个Solid
            if hasattr(solidObject, 'ShapeType') and solidObject.ShapeType() == 0:  # Compound
                # 尝试从CadQuery对象获取solids
                if hasattr(cadqueryObject, 'solids'):
                    solids = cadqueryObject.solids()
                    if solids.size() > 0:
                        solidObject = solids.val()
            
            # 转换为底层 TopoDS_Shape
            shapeOcp = solidObject.wrapped
            
            # 创建 GProps 对象用于存储质量属性
            propsVolume = GProp_GProps()
            
            # 计算体积属性
            BRepGProp.VolumeProperties_s(shapeOcp, propsVolume)
            
            # 获取基本几何属性
            volumeMm3 = propsVolume.Mass()  # 体积 (mm³)
            volumeM3 = volumeMm3 * 1e-9     # 转换为 m³
            
            # 计算质量
            massKg = volumeM3 * self.densityMaterial
            
            # 获取质心位置
            centerMass = propsVolume.CentreOfMass()
            centerMassX = centerMass.X()
            centerMassY = centerMass.Y()
            centerMassZ = centerMass.Z()
            
            # 获取惯性张量矩阵
            inertiaMatrix = propsVolume.MatrixOfInertia()
            
            # 提取惯性张量元素 (单位转换：mm⁴ → kg·m²)
            conversionFactor = self.densityMaterial * 1e-12  # kg/m³ * mm⁴ → kg·m²
            
            inertiaXx = inertiaMatrix.Value(1, 1) * conversionFactor
            inertiaYy = inertiaMatrix.Value(2, 2) * conversionFactor
            inertiaZz = inertiaMatrix.Value(3, 3) * conversionFactor
            inertiaXy = inertiaMatrix.Value(1, 2) * conversionFactor
            inertiaXz = inertiaMatrix.Value(1, 3) * conversionFactor
            inertiaYz = inertiaMatrix.Value(2, 3) * conversionFactor
            
            return {
                'volumeMm3': volumeMm3,
                'volumeM3': volumeM3,
                'massKg': massKg,
                'centerMassMm': {
                    'x': centerMassX,
                    'y': centerMassY,
                    'z': centerMassZ
                },
                'centerMassM': {
                    'x': centerMassX * 1e-3,
                    'y': centerMassY * 1e-3,
                    'z': centerMassZ * 1e-3
                },
                'inertiaMatrixKgM2': {
                    'ixx': inertiaXx,
                    'iyy': inertiaYy,
                    'izz': inertiaZz,
                    'ixy': inertiaXy,
                    'ixz': inertiaXz,
                    'iyz': inertiaYz
                },
                'densityKgM3': self.densityMaterial
            }
            
        except Exception as e:
            print(f"计算质量属性时出现错误: {e}")
            return None
    
    def printMassPropertiesF(self, massProperties):
        """
        打印质量属性信息
        
        Args:
            massProperties (dict): 质量属性字典
        """
        if massProperties is None:
            print("无法打印质量属性：计算失败")
            return
        
        print("=" * 60)
        print("基于OCP的几何实体质量属性计算结果")
        print("=" * 60)
        print(f"材料密度: {massProperties['densityKgM3']:.1f} kg/m³")
        print()
        
        print("体积信息:")
        print(f"  体积: {massProperties['volumeMm3']:.2f} mm³")
        print(f"  体积: {massProperties['volumeM3']:.6e} m³")
        print()
        
        print("质量信息:")
        print(f"  质量: {massProperties['massKg']:.6f} kg")
        print(f"  质量: {massProperties['massKg'] * 1000:.3f} g")
        print()
        
        print("质心位置:")
        centerMm = massProperties['centerMassMm']
        centerM = massProperties['centerMassM']
        print(f"  X: {centerMm['x']:.3f} mm ({centerM['x']:.6f} m)")
        print(f"  Y: {centerMm['y']:.3f} mm ({centerM['y']:.6f} m)")
        print(f"  Z: {centerMm['z']:.3f} mm ({centerM['z']:.6f} m)")
        print()
        
        print("惯性张量 (kg·m²):")
        inertia = massProperties['inertiaMatrixKgM2']
        print(f"  Ixx: {inertia['ixx']:.6e}")
        print(f"  Iyy: {inertia['iyy']:.6e}")
        print(f"  Izz: {inertia['izz']:.6e}")
        print(f"  Ixy: {inertia['ixy']:.6e}")
        print(f"  Ixz: {inertia['ixz']:.6e}")
        print(f"  Iyz: {inertia['iyz']:.6e}")
    
    def visualizeGeometryF(self, cadqueryObject, titleWindow="几何体可视化", colorMesh="lightblue", showMassProperties=True, blockingMode=False):
        """
        使用PyVista可视化CadQuery几何体，并可选择显示质量属性信息
        
        Args:
            cadqueryObject: CadQuery工作平面对象或实体对象
            titleWindow (str): 3D窗口标题
            colorMesh (str): 网格颜色
            showMassProperties (bool): 是否在可视化窗口中显示质量属性信息
            blockingMode (bool): 是否阻塞模式，False为非阻塞（默认），True为阻塞等待用户关闭窗口
        """
        try:
            # 获取实体对象
            if hasattr(cadqueryObject, 'val'):
                solidObject = cadqueryObject.val()
            else:
                solidObject = cadqueryObject
            
            # 创建临时STL文件
            with tempfile.NamedTemporaryFile(suffix=".stl", delete=False) as tempFileStl:
                cq.exporters.export(cadqueryObject, tempFileStl.name)
                pathStl = tempFileStl.name
            
            # 创建PyVista绘图器
            plotterPyvista = pv.Plotter(window_size=[1000, 800])
            plotterPyvista.title = titleWindow
            
            # 加载网格
            meshGeometry = pv.read(pathStl)
            
            # 添加网格到场景
            plotterPyvista.add_mesh(meshGeometry, color=colorMesh, show_edges=True, opacity=0.8)
            
            # 添加坐标轴
            plotterPyvista.add_axes(line_width=3, labels_off=False)
            
            # 添加网格基本信息
            # plotterPyvista.add_text(f"顶点数: {meshGeometry.n_points}", 
            #                       position='upper_left', font_size=12, color='black')
            # plotterPyvista.add_text(f"单元数: {meshGeometry.n_cells}", 
            #                       position='upper_left', font_size=10, color='black')
            
            # 如果需要显示质量属性信息
            if showMassProperties:
                propertiesMass = self.calculateMassPropertiesF(cadqueryObject)
                if propertiesMass:
                    # 在3D窗口中显示质量属性信息
                    textMassInfo = (
                        f"密度: {propertiesMass['densityKgM3']:.0f} kg/m³\n"
                        f"体积: {propertiesMass['volumeMm3']:.1f} mm³\n"
                        f"质量: {propertiesMass['massKg']*1000:.2f} g\n"
                        f"质心: ({propertiesMass['centerMassMm']['x']:.1f}, "
                        f"{propertiesMass['centerMassMm']['y']:.1f}, "
                        f"{propertiesMass['centerMassMm']['z']:.1f}) mm"
                    )
                    
                    font_file = r"C:\Windows\Fonts\simsun.ttc"    # 或 simhei.ttf
                    plotterPyvista.add_text(textMassInfo, 
                                          position='upper_left', 
                                          font_size=10, 
                                          color='darkblue',
                                          font_file=font_file)       # 关键参数：指定中文字体)
                    
                    # 添加质心点标记
                    centerPoint = [propertiesMass['centerMassMm']['x'],
                                 propertiesMass['centerMassMm']['y'],
                                 propertiesMass['centerMassMm']['z']]
                    
                    plotterPyvista.add_mesh(pv.Sphere(radius=1.0, center=centerPoint), 
                                          color='red', 
                                          point_size=10)
                    
                    plotterPyvista.add_text("● 质心", 
                                          position='upper_right', 
                                          font_size=10, 
                                          color='red',
                                          font_file=font_file)       # 关键参数：指定中文字体)
            
            # 设置相机视角
            plotterPyvista.camera_position = 'iso'
            
            # 显示窗口
            print(f"正在显示 {titleWindow}...")
            if blockingMode:
                print("关闭3D窗口以继续程序执行")
                plotterPyvista.show()
            else:
                print("非阻塞模式：程序将继续执行，3D窗口在后台显示")
                plotterPyvista.show(auto_close=False, interactive=False)
            
            # 清理临时文件
            try:
                os.unlink(pathStl)
            except:
                pass
                
        except Exception as e:
            print(f"可视化过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def visualizeWithMassPropertiesF(self, cadqueryObject, titleWindow="几何体质量属性可视化", blockingMode=False):
        """
        可视化几何体并显示详细的质量属性信息
        
        Args:
            cadqueryObject: CadQuery工作平面对象或实体对象
            titleWindow (str): 3D窗口标题
            blockingMode (bool): 是否阻塞模式，False为非阻塞（默认），True为阻塞等待用户关闭窗口
        """
        # 先打印详细的质量属性信息
        propertiesMass = self.calculateMassPropertiesF(cadqueryObject)
        self.printMassPropertiesF(propertiesMass)
        
        # 然后进行3D可视化
        self.visualizeGeometryF(cadqueryObject, titleWindow, "gold", True, blockingMode)

# 主程序
if __name__ == "__main__":
    # 创建几何对象
    workplaneBox = cq.Workplane("XY").box(10, 20, 30)

    # 创建质量属性计算器，使用指定密度
    calculatorMass = MassPropertiesOcpC(densityMaterial=2350.0)

    print("\n" + "="*50)
    print("PyVista可视化演示（非阻塞模式）")
    print("="*50)
    
    # 演示基本可视化功能
    print("\n1. 基本几何体可视化（立方体）")
    calculatorMass.visualizeGeometryF(workplaneBox, "立方体可视化", "lightblue", True)  # 默认非阻塞
    
    # 计算质量属性
    propertiesMass = calculatorMass.calculateMassPropertiesF(workplaneBox)
    
    # 打印结果
    calculatorMass.printMassPropertiesF(propertiesMass)
        
    # 创建更复杂的几何体进行演示
    print("\n2. 复杂几何体可视化（带孔圆柱体）")
    cylinderWithHole = (cq.Workplane("XY")
                       .circle(20)
                       .extrude(30)
                       .faces(">Z")
                       .circle(10)
                       .cutThruAll())
    
    # 使用完整的质量属性可视化方法（非阻塞）
    calculatorMass.visualizeWithMassPropertiesF(cylinderWithHole, "带孔圆柱体质量属性可视化")  # 默认非阻塞
    
    print("\n可视化演示完成！程序继续执行，3D窗口在后台保持打开")
    print("提示：如需阻塞模式，请设置 blockingMode=True")
