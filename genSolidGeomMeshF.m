function [model, nodes, elements] = genSolidGeomMeshF(params, hmax)
% 生成浮筒平台几何体和有限元网格
% 输入：params（结构体，包含所有尺寸参数），hmax（网格最大尺寸）
% 输出：model, nodes, elements
% 生成浮筒平台几何体和有限元网格
% 输入：params（结构体，包含所有尺寸参数），hmax（网格最大尺寸），params.plotGeom（是否绘制几何体），params.plotMesh（是否绘制网格）
% 输出：model, nodes, elements

% 参数解包
outerRadius    = params.outerRadius;
wallThickness  = params.wallThickness;
crossThickness = params.crossThickness;
cylinderHeight = params.cylinderHeight;
floatLength    = params.floatLength;
floatWidth     = params.floatWidth;
floatHeight    = params.floatHeight;
crossbarLength = params.crossbarLength;
crossbarWidth  = params.crossbarWidth;
crossbarHeight = params.crossbarHeight;
crossbarZ      = params.crossbarZ;
sideLength     = params.sideLength;
sideWidth      = params.sideWidth;
sideHeight     = params.sideHeight;
numFloats      = params.numFloats;
angleStep      = 360 / numFloats;

% 中柱
cylinderOuter = multicylinder(outerRadius, cylinderHeight);
cylinderInner = multicylinder(outerRadius - wallThickness, cylinderHeight - 2*wallThickness);
cylinderInner = translate(cylinderInner , [0 , 0 , wallThickness]);

% 浮筒
floatBoxOuter = multicuboid(floatLength, floatWidth, floatHeight);
floatBoxOuter = fegeometry(floatBoxOuter);
floatBoxInner = multicuboid(floatLength - 2*wallThickness, floatWidth - 2*wallThickness, floatHeight - 2*wallThickness);
floatBoxInner = translate(floatBoxInner, [floatLength/2, 0, wallThickness]);
floatBoxInner = fegeometry(floatBoxInner);
floatBox = translate(floatBoxOuter, [floatLength/2, 0, 0]);

% 横撑
crossbarOuter = multicuboid(crossbarLength, crossbarWidth, crossbarHeight);
crossbarInner = multicuboid(crossbarLength - 2 * crossThickness, crossbarWidth  - 2 * crossThickness, crossbarHeight - 2 * crossThickness);
crossbarInner = translate(crossbarInner, [crossbarLength/2, 0, crossbarZ+crossThickness]);
crossbar = translate(crossbarOuter, [crossbarLength/2, 0, crossbarZ]);

% 外立柱
sideColumnOuter = multicuboid(sideLength, sideWidth, sideHeight);
sideColumnOuter = fegeometry(sideColumnOuter);
sideColumnInner = multicuboid(sideLength - 2 * wallThickness, sideWidth - 2 * wallThickness, sideHeight - 2 * wallThickness);
sideColumnInner = translate(sideColumnInner, [0, 0 , wallThickness]);
sideColumnInner = fegeometry(sideColumnInner);
sideColumn = subtract(sideColumnOuter, sideColumnInner);
sideColumn = translate(sideColumn, [floatLength + sideLength/2 , 0 , 0]);

% 合并
combinedFloats = union(floatBox, crossbar);
combinedFloats = union(combinedFloats, sideColumn);
combinedFloats1 = rotate(combinedFloats,   angleStep, [0, 0, 0]);
combinedFloats2 = rotate(combinedFloats, 2*angleStep, [0, 0, 0]);
combinedFloats = union(combinedFloats, combinedFloats1);
combinedFloats = union(combinedFloats, combinedFloats2);
combinedFloats = union(combinedFloats, cylinderOuter);
combinedFloats = subtract(combinedFloats, cylinderInner);
combinedFloats = subtract(combinedFloats, floatBoxInner);
floatBoxInner  = rotate(floatBoxInner, angleStep, [0, 0, 0]);
combinedFloats = subtract(combinedFloats, floatBoxInner);
floatBoxInner  = rotate(floatBoxInner, angleStep, [0, 0, 0]);
combinedFloats = subtract(combinedFloats, floatBoxInner);
combinedFloats = subtract(combinedFloats, crossbarInner);
crossbarInner  = rotate(crossbarInner, angleStep, [0, 0, 0]);
combinedFloats = subtract(combinedFloats, crossbarInner);
crossbarInner  = rotate(crossbarInner, angleStep, [0, 0, 0]);
finalGeometry = subtract(combinedFloats, crossbarInner);

% 可视化几何体
if isfield(params, 'plotGeom') && params.plotGeom
    figure;
    pdegplot(finalGeometry, 'FaceLabels', 'on', 'FaceAlpha', 0.25);
    title('浮筒平台几何模型（外壁厚度 0.45 m,横撑厚度0.5m）');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
end

% 网格生成
model = femodel('geometry', finalGeometry);
model = generateMesh(model, 'Hmax', hmax);
nodes = model.Mesh.Nodes;
elements = model.Mesh.Elements;

% 可视化网格
if isfield(params, 'plotMesh') && params.plotMesh
    figure;
    pdemesh(model);
    title('浮筒平台有限元网格');
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
end
end

