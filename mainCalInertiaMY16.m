% 清空环境
clc; clear; close all;

% 添加路径
% addpath('+yaml'); % YAML工具包

%% 1. 读取配置参数

% 读取YAML配置文件
configFile = fullfile(pwd, 'config', 'platformConfig.yaml');
try
    % 使用yaml.loadFile函数读取配置
    configData = yaml.loadFile(configFile, "ConvertToArray", true);
catch ME
    error('无法读取YAML配置文件：%s\n请检查文件路径和格式是否正确。', ME.message);
end

% 创建参数结构体
params = struct();

% 基本参数
params.numFloats = configData.basicParams.numFloats;
params.densityMaterial = configData.basicParams.densityMaterial;

% 中心柱参数
params.outerRadius = configData.centerColumn.outerRadius;
params.wallThickness = configData.centerColumn.wallThickness;
params.cylinderHeight = configData.centerColumn.cylinderHeight;

% 浮筒参数
params.floatLength = configData.floaters.length;
params.floatWidth = configData.floaters.width;
params.floatHeight = configData.floaters.height;

% 横撑参数
params.crossThickness = configData.crossbars.thickness;
params.crossbarLength = configData.crossbars.length;
params.crossbarWidth = configData.crossbars.width;
params.crossbarHeight = configData.crossbars.height;
params.crossbarZ = configData.crossbars.heightZ;

% 外立柱参数
params.sideLength = configData.sideColumns.length;
params.sideWidth = configData.sideColumns.width;
params.sideHeight = configData.sideColumns.height;

%% 2. 生成几何模型和网格
% 设置可视化选项
params.plotGeom = configData.visualization.plotGeom;
params.plotMesh = configData.visualization.plotMesh;
params.plotSlice = configData.visualization.plotSlice;

% 网格参数
meshSize = configData.visualization.meshSize;

% 生成几何模型和网格
[modelFEM, nodesCoord, elementsConnect] = genSolidGeomMeshF(params, meshSize);

%% 3. 计算质心和惯性张量
% 计算几何特性
[centerMass, tensorInertia] = calGeomPropF(modelFEM, nodesCoord, elementsConnect, params.densityMaterial);

% 输出结果
disp('计算结果:');
disp(['质心位置 (m): [', num2str(centerMass'), ']']);
disp('惯性张量 (kg·m²):');
disp(tensorInertia);

%% 4. 可视化切片（可选）
if params.plotSlice
    visualizeMeshSlice(modelFEM, 'position', 10, 'drctSlice', 'z', 'drctHold', '+')
end

